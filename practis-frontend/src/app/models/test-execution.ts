import { TestCase } from './test-case';
import { TestRun } from './test-run';

export interface Attachment {
    name: string;
    url: string;
}

export class TestExecution {
    uuid?: string;
    date?: Date;
    status?: string;
    report?: string;
    test_case?: TestCase | string;
    test_run?: TestRun | string;
    attachments?: Attachment[];

    constructor(init?: Partial<TestExecution>) {
        Object.assign(this, init);
    }
}
