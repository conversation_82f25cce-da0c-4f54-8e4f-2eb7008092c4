import pytest
from unittest import mock
import json
import time
from django.utils import timezone as tz

pytestmark = pytest.mark.django_db


def mocked_requests_post(*args, **kwargs):
    class MockResponse:
        def __init__(self, json_data, status_code):
            self.json_data = json_data
            self.status_code = status_code
            self.content = json.dumps(json_data).encode()

        def json(self):
            return self.json_data

    return MockResponse({"id": 2}, 200)


class Test_TestExecutionView:
    endpoint = "/api/testexecution/"

    def create_test_execution_object(self, api_client):
        response = api_client().post(
            self.endpoint,
            {
                "date": tz.localtime(tz.now()),
                "result": "pass",
                "report": "http://report.com",
                "test_case": 1,
                "test_run": 1,
                "tcms_external_id": "1",
            },
            format="json",
        )

        return response

    def test_list(self, test_execution_factory, api_client):
        # Arrange

        test_execution_factory()

        # Act

        response = api_client().get(self.endpoint)

        # Assert
        assert response.status_code == 200
        assert len(response.data) == 1

    def test_retrieve(self, test_execution_factory, api_client):
        test_execution_factory()
        response = api_client().get(f"{self.endpoint}1/")
        assert response.status_code == 200
        assert response.data["status"] == "PASSED"
        assert response.data["report"] == "http://report.com"
        assert response.data["test_case"] == 1
        assert response.data["test_run"] == 1

    def test_create(self, api_client):
        response = self.create_test_execution_object(api_client)
        assert response.status_code == 201

    @mock.patch("requests.put", side_effect=mocked_requests_post)
    def test_update(self, mock_put, api_client):
        reponse = self.create_test_execution_object(api_client)
        assert reponse.status_code == 201

        response = api_client().put(
            f"{self.endpoint}1/",
            {
                "date": tz.localtime(tz.now()),
                "status": "IDLE",
                "report": "http://report.com",
                "test_case": 1,
                "test_run": 1,
                "tcms_external_id": 1,
            },
            format="json",
        )
        assert response.status_code == 200
        assert response.data["status"] == "IDLE"

    @mock.patch("requests.put", side_effect=mocked_requests_post)
    def test_update_status_sets_date_automatically(self, mock_put, api_client):
        # Create a test execution
        response = self.create_test_execution_object(api_client)
        assert response.status_code == 201

        # Get the initial date
        initial_response = api_client().get(f"{self.endpoint}1/")
        initial_date = initial_response.data["date"]

        # Wait a moment to ensure time difference
        time.sleep(0.1)

        # Update the status without providing a date
        response = api_client().put(
            f"{self.endpoint}1/",
            {
                "status": "FAILED",
                "report": "http://report.com",
                "test_case": 1,
                "test_run": 1,
                "tcms_external_id": 1,
            },
            format="json",
        )

        assert response.status_code == 200
        assert response.data["status"] == "FAILED"

        # Verify that the date was automatically updated
        updated_date = response.data["date"]
        assert updated_date is not None
        assert updated_date != initial_date  # Date should be different (newer)

    @mock.patch("requests.put", side_effect=mocked_requests_post)
    def test_update_same_status_does_not_change_date(self, mock_put, api_client):
        # Create a test execution with initial status
        response = self.create_test_execution_object(api_client)
        assert response.status_code == 201

        # Get the initial date and status
        initial_response = api_client().get(f"{self.endpoint}1/")
        initial_date = initial_response.data["date"]
        initial_status = initial_response.data["status"]

        # Wait a moment to ensure time difference would be detectable
        import time
        time.sleep(0.1)

        # Update with the same status
        response = api_client().put(
            f"{self.endpoint}1/",
            {
                "status": initial_status,  # Same status as before
                "report": "http://updated-report.com",
                "test_case": 1,
                "test_run": 1,
                "tcms_external_id": 1,
            },
            format="json",
        )

        assert response.status_code == 200
        assert response.data["status"] == initial_status

        # Verify that the date was NOT automatically updated
        updated_date = response.data["date"]
        assert updated_date == initial_date  # Date should remain the same

    @mock.patch("requests.delete", side_effect=mocked_requests_post)
    def test_destroy(self, mock_delete, api_client):
        response = self.create_test_execution_object(api_client)

        assert response.status_code == 201

        response = api_client().delete(f"{self.endpoint}1/")
        assert response.status_code == 204
        assert response.data["message"] == "The object has been deleted"

        response = api_client().get(f"{self.endpoint}1/")
        assert response.status_code == 404
