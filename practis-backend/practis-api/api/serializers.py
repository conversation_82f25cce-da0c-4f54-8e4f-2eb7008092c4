from rest_framework import serializers
from .models import *
from diagram.serializers import MinimalParameterTypeSerializer
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone


class ImageFileSerializer(serializers.ModelSerializer):
    class Meta:
        model = ImageFile
        fields = "__all__"


class SutProviderSerializer(serializers.ModelSerializer):
    class Meta:
        model = SutProvider
        fields = "__all__"


class SutProviderGETSerializer(serializers.ModelSerializer):
    images = ImageFileSerializer(many=True)

    class Meta:
        model = SutProvider
        fields = "__all__"


class SutGETSerializer(serializers.ModelSerializer):
    sut_provider = SutProviderSerializer()
    images = ImageFileSerializer(many=True)

    class Meta:
        model = Sut
        fields = "__all__"


class NoteSerializer(serializers.ModelSerializer):

    content_type = serializers.SlugRelatedField(
        queryset=ContentType.objects.all(), slug_field="model"
    )

    class Meta:
        model = Note
        fields = "__all__"


class VersionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Version
        fields = "__all__"


class VersionGETSerializer(serializers.ModelSerializer):
    sut = SutGETSerializer()
    images = ImageFileSerializer(many=True)

    class Meta:
        model = Version
        fields = "__all__"


class VersionWithImagesSerializer(serializers.ModelSerializer):
    images = ImageFileSerializer(many=True)

    class Meta:
        model = Version
        fields = "__all__"


class RiskSerializer(serializers.ModelSerializer):

    class Meta:
        model = Risk
        fields = "__all__"


class RiskAnalysisWithRisksSerializer(serializers.ModelSerializer):
    risks = RiskSerializer(many=True)

    class Meta:
        model = RiskAnalysis
        fields = "__all__"


class RequirementSerializer(serializers.ModelSerializer):
    class Meta:
        model = Requirement
        fields = "__all__"


class ObjectivesAndScopeSerializer(serializers.ModelSerializer):

    class Meta:
        model = ObjectivesAndScope
        fields = "__all__"


class SutSerializer(serializers.ModelSerializer):

    class Meta:
        model = Sut
        fields = "__all__"


class SutGETWithVersionsSerializer(serializers.ModelSerializer):
    sut_provider = SutProviderGETSerializer()
    images = ImageFileSerializer(many=True)
    versions = VersionWithImagesSerializer(many=True)

    class Meta:
        model = Sut
        fields = "__all__"


class ObjectivesAndScopeGETSerializer(serializers.ModelSerializer):
    sut_version = VersionGETSerializer()

    class Meta:
        model = ObjectivesAndScope
        fields = "__all__"


class FlowSerializer(serializers.ModelSerializer):

    class Meta:
        model = Flow
        fields = "__all__"


class FlowGETSerializer(serializers.ModelSerializer):
    parameter_type = MinimalParameterTypeSerializer(many=True)

    class Meta:
        model = Flow
        fields = "__all__"


class FlowExecutionSerializer(serializers.ModelSerializer):

    class Meta:
        model = FlowExecution
        fields = "__all__"


class FlowExecutionSerializer(serializers.ModelSerializer):

    class Meta:
        model = FlowExecution
        fields = "__all__"


class FlowExecutionGETSerializer(serializers.ModelSerializer):
    sut_version = VersionSerializer()
    flow = FlowGETSerializer()

    class Meta:
        model = FlowExecution
        fields = "__all__"


class MiniFlowExecutionGETSerializer(serializers.ModelSerializer):
    flow = FlowGETSerializer()

    class Meta:
        model = FlowExecution
        fields = "__all__"


class VersionGETAllDataSerializer(serializers.ModelSerializer):
    sut = SutSerializer()
    images = ImageFileSerializer(many=True)
    objectives = ObjectivesAndScopeSerializer(many=True)
    riskanalysis = RiskAnalysisWithRisksSerializer(many=True)
    requirements = RequirementSerializer(many=True)
    flowexecutions = MiniFlowExecutionGETSerializer(many=True)

    class Meta:
        model = Version
        fields = "__all__"

class TestRunSerializer(serializers.ModelSerializer):
    attachments = serializers.JSONField(read_only=True)
    class Meta:
        model = TestRun
        fields = "__all__"

    def validate(self, attrs):
        instance = TestRun(**attrs)
        instance.full_clean()
        return attrs


class RiskAnalysisSerializer(serializers.ModelSerializer):

    class Meta:
        model = RiskAnalysis
        fields = "__all__"


class RiskAnalysisGETSerializer(serializers.ModelSerializer):
    sut_version = VersionGETSerializer()

    class Meta:
        model = RiskAnalysis
        fields = "__all__"


class RiskGETSerializer(serializers.ModelSerializer):
    risk_analysis = RiskAnalysisSerializer()

    class Meta:
        model = Risk
        fields = "__all__"


class RequirementGETSerializer(serializers.ModelSerializer):
    sut_version = VersionGETSerializer()

    class Meta:
        model = Requirement
        fields = "__all__"


class VulnerabilitySerializer(serializers.ModelSerializer):

    class Meta:
        model = Vulnerability
        fields = "__all__"


class VulnerabilityGETSerializer(serializers.ModelSerializer):
    risks = RiskSerializer(many=True)

    class Meta:
        model = Vulnerability
        fields = "__all__"


class TestCaseSerializer(serializers.ModelSerializer):
    attachments = serializers.JSONField(read_only=True)

    class Meta:
        model = TestCase
        fields = "__all__"


class TestCaseGETSerializer(serializers.ModelSerializer):
    vulnerability = VulnerabilityGETSerializer()
    requirement = RequirementGETSerializer()
    attachments = serializers.JSONField(read_only=True)

    class Meta:
        model = TestCase
        exclude = ["file"]

class TestPlanSerializer(serializers.ModelSerializer):
    file = serializers.FileField(required=False)
    attachments = serializers.JSONField(read_only=True)

    class Meta:
        model = TestPlan
        fields = "__all__"

class TestPlanGETSerializer(serializers.ModelSerializer):
    file = serializers.FileField(required=False)
    attachments = serializers.JSONField(read_only=True)
    test_cases = TestCaseSerializer(many=True)

    class Meta:
        model = TestPlan
        fields = "__all__"


class TestRunGETSerializer(serializers.ModelSerializer):
    attachments = serializers.JSONField(read_only=True)
    plan = TestPlanSerializer()

    class Meta:
        model = TestRun
        fields = "__all__"

class TestExecutionSerializer(serializers.ModelSerializer):
    class Meta:
        model = TestExecution
        fields = "__all__"

    def update(self, instance, validated_data):
        # If status is being updated to a different value,
        # automatically set the date to current time
        if 'status' in validated_data and validated_data['status'] != instance.status:
            validated_data['date'] = timezone.now()

        return super().update(instance, validated_data)
